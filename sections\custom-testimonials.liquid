<link rel="stylesheet" href="{{ 'component-slider.css' | asset_url }}" media="print" onload="this.media='all'">
<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>

<div class="testimonial-slider-section page-width">
  {% if section.settings.title != blank %}
    <h2 class="testimonial-slider__title">{{ section.settings.title | escape }}</h2>
  {% endif %}

  <slider-component class="slider-mobile-gutter">
    <ul class="testimonial-slider slider slider--desktop"
        id="Slider-{{ section.id }}"
        role="list"
    >
      {%- for block in section.blocks -%}
        <li id="Slide-{{ section.id }}-{{ forloop.index }}"
            class="testimonial-slider__slide slider__slide"
            {{ block.shopify_attributes }}
        >
          <div class="testimonial-card">
            <div class="testimonial-card__stars" role="img" aria-label="{{ block.settings.rating }} out of 5 stars">
              {%- for i in (1..5) -%}
                <span class="testimonial-card__star">★</span>
              {%- endfor -%}
            </div>
            <div class="testimonial-card__text">
              {{ block.settings.text }}
            </div>
            <div class="testimonial-card__author">
              {{ block.settings.author | escape }}
            </div>
            {%- if block.settings.show_verified -%}
              <div class="testimonial-card__verified">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="testimonial-card__verified-icon">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                </svg>
                Verified Buyer
              </div>
            {%- endif -%}
          </div>
        </li>
      {%- endfor -%}
    </ul>
    
    <div class="slider-counter slider-counter--dots">
      {%- for block in section.blocks -%}
        <button class="slider-counter__link slider-counter__link--dots" aria-label="Slide {{ forloop.index }} of {{ forloop.length }}">
        </button>
      {%- endfor -%}
    </div>
  </slider-component>
</div>

<style>
  .testimonial-slider-section {
    padding: 4rem 0;
    background-color: #f8f6f3;
  }

  .testimonial-slider__title {
    font-family: 'Times New Roman', serif;
    font-size: 2.5rem;
    font-weight: 400;
    text-align: center;
    margin-bottom: 3rem;
    color: #8B4513;
    line-height: 1.2;
  }

  .testimonial-slider {
    --grid-desktop-horizontal-spacing: 1.5rem;
    --grid-desktop-items-per-row: 3;
    margin-bottom: 3rem;
    display: flex;
    gap: var(--grid-desktop-horizontal-spacing);
  }

  .testimonial-slider__slide {
    flex: 0 0 calc((100% - (var(--grid-desktop-horizontal-spacing) * 2)) / 3);
    max-width: calc((100% - (var(--grid-desktop-horizontal-spacing) * 2)) / 3);
  }

  .testimonial-card {
    background-color: #f5f2ed;
    border-radius: 1rem;
    padding: 2rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
  }

  .testimonial-card:hover {
    transform: translateY(-2px);
  }

  .testimonial-card__stars {
    display: flex;
    justify-content: center;
    gap: 0.2rem;
    margin-bottom: 1.5rem;
    color: #8B4513;
    font-size: 1.2rem;
  }

  .testimonial-card__star {
    display: inline-block;
  }

  .testimonial-card__text {
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex-grow: 1;
    color: #333;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .testimonial-card__author {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
    font-size: 1rem;
  }

  .testimonial-card__verified {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    font-size: 0.85rem;
    color: #22c55e;
    font-weight: 500;
  }

  .testimonial-card__verified-icon {
    width: 1rem;
    height: 1rem;
  }

  .slider-counter--dots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
  }

  .slider-counter__link--dots {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background-color: #d1d5db;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .slider-counter__link--dots:hover,
  .slider-counter__link--dots.is-active {
    background-color: #8B4513;
  }

  /* Tablet */
  @media screen and (max-width: 989px) {
    .testimonial-slider {
      --grid-desktop-items-per-row: 2;
    }
    
    .testimonial-slider__slide {
      flex: 0 0 calc((100% - var(--grid-desktop-horizontal-spacing)) / 2);
      max-width: calc((100% - var(--grid-desktop-horizontal-spacing)) / 2);
    }
    
    .testimonial-slider__title {
      font-size: 2.2rem;
    }
  }

  /* Mobile */
  @media screen and (max-width: 749px) {
    .testimonial-slider-section {
      padding: 3rem 1rem;
    }
    
    .testimonial-slider__title {
      font-size: 1.8rem;
      margin-bottom: 2rem;
    }
    
    .testimonial-slider {
      display: block;
      overflow-x: auto;
      scroll-snap-type: x mandatory;
      -webkit-overflow-scrolling: touch;
      padding: 0 1rem;
      margin: 0 -1rem 2rem -1rem;
    }
    
    .testimonial-slider__slide {
      flex: 0 0 85%;
      max-width: 85%;
      scroll-snap-align: center;
      margin-right: 1rem;
    }
    
    .testimonial-card {
      padding: 1.5rem;
    }
    
    .testimonial-card__text {
      font-size: 0.9rem;
    }
    
    .slider-counter--dots {
      margin-top: 1.5rem;
    }
  }

  @media screen and (max-width: 480px) {
    .testimonial-slider__slide {
      flex: 0 0 90%;
      max-width: 90%;
    }
    
    .testimonial-slider__title {
      font-size: 1.6rem;
    }
  }
</style>

{% schema %}
{
  "name": "Custom Testimonials",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "default": "Hear from Our Customers",
      "label": "Title"
    }
  ],
  "blocks": [
    {
      "type": "testimonial",
      "name": "Testimonial",
      "limit": 9,
      "settings": [
        {
          "type": "range",
          "id": "rating",
          "min": 1,
          "max": 5,
          "step": 1,
          "default": 5,
          "label": "Star rating"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>The variety of flavors they offer is incredible, and each one is more delicious than the last.</p>",
          "label": "Testimonial text"
        },
        {
          "type": "text",
          "id": "author",
          "default": "Anna Lou",
          "label": "Author name"
        },
        {
          "type": "text",
          "id": "product",
          "label": "Product purchased (optional)"
        },
        {
          "type": "checkbox",
          "id": "show_verified",
          "default": true,
          "label": "Show 'Verified Buyer' badge"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Custom Testimonials",
      "blocks": [
        {
          "type": "testimonial"
        },
        {
          "type": "testimonial"
        },
        {
          "type": "testimonial"
        }
      ]
    }
  ]
}
{% endschema %}
