<link rel="stylesheet" href="{{ 'component-slider.css' | asset_url }}" media="print" onload="this.media='all'">
<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>

<div class="testimonial-slider-section page-width">
  {% if section.settings.title != blank %}
    <h2 class="testimonial-slider__title">{{ section.settings.title | escape }}</h2>
  {% endif %}

  <div class="testimonial-slider-wrapper">
    <button class="testimonial-slider__arrow testimonial-slider__arrow--prev" aria-label="Previous testimonials">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
      </svg>
    </button>

    <slider-component class="slider-mobile-gutter">
      <ul class="testimonial-slider slider slider--desktop"
          id="Slider-{{ section.id }}"
          role="list"
      >
        {%- for block in section.blocks -%}
          <li id="Slide-{{ section.id }}-{{ forloop.index }}"
              class="testimonial-slider__slide slider__slide"
              {{ block.shopify_attributes }}
          >
            <div class="testimonial-card">
              <div class="testimonial-card__stars" role="img" aria-label="{{ block.settings.rating }} out of 5 stars">
                {%- for i in (1..5) -%}
                  {%- if i <= block.settings.rating -%}
                    <span class="testimonial-card__star testimonial-card__star--filled">★</span>
                  {%- else -%}
                    <span class="testimonial-card__star">☆</span>
                  {%- endif -%}
                {%- endfor -%}
              </div>
              <div class="testimonial-card__text">
                {{ block.settings.text }}
              </div>
              <div class="testimonial-card__author">
                {{ block.settings.author | escape }}
              </div>
              {%- if block.settings.show_verified -%}
                <div class="testimonial-card__verified">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="testimonial-card__verified-icon">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                  </svg>
                  Verified Buyer
                </div>
              {%- endif -%}
            </div>
          </li>
        {%- endfor -%}
      </ul>

      <div class="slider-counter slider-counter--dots">
        {%- for block in section.blocks -%}
          <button class="slider-counter__link slider-counter__link--dots" aria-label="Slide {{ forloop.index }} of {{ forloop.length }}">
          </button>
        {%- endfor -%}
      </div>
    </slider-component>

    <button class="testimonial-slider__arrow testimonial-slider__arrow--next" aria-label="Next testimonials">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
      </svg>
    </button>
  </div>
</div>

<style>
  .testimonial-slider-section {
    padding: 5rem 0;
    background-color: #ffffff;
  }

  .testimonial-slider__title {
    font-family: 'Times New Roman', serif;
    font-size: 2.5rem;
    font-weight: 400;
    text-align: center;
    margin-bottom: 4rem;
    color: #333;
    line-height: 1.2;
  }

  .testimonial-slider-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    gap: 2rem;
  }

  .testimonial-slider__arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    background-color: #ffffff;
    border: 2px solid #e2dcec;
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }

  .testimonial-slider__arrow:hover {
    background-color: #e2dcec;
    transform: translateY(-50%) scale(1.1);
  }

  .testimonial-slider__arrow--prev {
    left: -1.5rem;
  }

  .testimonial-slider__arrow--next {
    right: -1.5rem;
  }

  .testimonial-slider__arrow svg {
    width: 1.2rem;
    height: 1.2rem;
    color: #666;
  }

  .testimonial-slider {
    --grid-desktop-horizontal-spacing: 2rem;
    --grid-desktop-items-per-row: 3;
    margin-bottom: 3rem;
    display: flex;
    gap: var(--grid-desktop-horizontal-spacing);
    overflow-x: auto;
    scroll-behavior: smooth;
    padding: 0 1rem;
  }

  .testimonial-slider__slide {
    flex: 0 0 calc((100% - (var(--grid-desktop-horizontal-spacing) * 2)) / 3);
    max-width: calc((100% - (var(--grid-desktop-horizontal-spacing) * 2)) / 3);
  }

  .testimonial-card {
    background-color: #e2dcec;
    border-radius: 20px;
    padding: 2.5rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(255,255,255,0.2);
  }

  .testimonial-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
  }

  .testimonial-card__stars {
    display: flex;
    justify-content: center;
    gap: 0.3rem;
    margin-bottom: 2rem;
    font-size: 1.4rem;
  }

  .testimonial-card__star {
    display: inline-block;
    color: #ddd;
    transition: color 0.2s ease;
  }

  .testimonial-card__star--filled {
    color: #fbbf24;
  }

  .testimonial-card__text {
    font-size: 1rem;
    line-height: 1.7;
    margin-bottom: 2rem;
    flex-grow: 1;
    color: #444;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
  }

  .testimonial-card__author {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.1rem;
  }

  .testimonial-card__verified {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #22c55e;
    font-weight: 500;
  }

  .testimonial-card__verified-icon {
    width: 1.1rem;
    height: 1.1rem;
  }

  .slider-counter--dots {
    display: flex;
    justify-content: center;
    gap: 0.8rem;
    margin-top: 3rem;
  }

  .slider-counter__link--dots {
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background-color: #d1d5db;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .slider-counter__link--dots:hover,
  .slider-counter__link--dots.is-active {
    background-color: #8B4513;
    transform: scale(1.2);
  }

  /* Tablet */
  @media screen and (max-width: 989px) {
    .testimonial-slider {
      --grid-desktop-items-per-row: 2;
      --grid-desktop-horizontal-spacing: 1.5rem;
    }

    .testimonial-slider__slide {
      flex: 0 0 calc((100% - var(--grid-desktop-horizontal-spacing)) / 2);
      max-width: calc((100% - var(--grid-desktop-horizontal-spacing)) / 2);
    }

    .testimonial-slider__title {
      font-size: 2.2rem;
      margin-bottom: 3rem;
    }

    .testimonial-card {
      padding: 2rem;
    }

    .testimonial-slider__arrow {
      width: 2.5rem;
      height: 2.5rem;
    }

    .testimonial-slider__arrow--prev {
      left: -1rem;
    }

    .testimonial-slider__arrow--next {
      right: -1rem;
    }
  }

  /* Mobile */
  @media screen and (max-width: 749px) {
    .testimonial-slider-section {
      padding: 4rem 1rem;
    }

    .testimonial-slider__title {
      font-size: 1.8rem;
      margin-bottom: 3rem;
    }

    .testimonial-slider-wrapper {
      gap: 1rem;
    }

    .testimonial-slider {
      display: flex;
      overflow-x: auto;
      scroll-snap-type: x mandatory;
      -webkit-overflow-scrolling: touch;
      padding: 0 1rem;
      margin: 0 -1rem 2rem -1rem;
      scrollbar-width: none;
      -ms-overflow-style: none;
    }

    .testimonial-slider::-webkit-scrollbar {
      display: none;
    }

    .testimonial-slider__slide {
      flex: 0 0 85%;
      max-width: 85%;
      scroll-snap-align: center;
      margin-right: 1.5rem;
    }

    .testimonial-card {
      padding: 2rem;
      border-radius: 20px;
    }

    .testimonial-card__text {
      font-size: 0.95rem;
      margin-bottom: 1.5rem;
    }

    .testimonial-card__stars {
      margin-bottom: 1.5rem;
      font-size: 1.2rem;
    }

    .testimonial-card__author {
      font-size: 1rem;
      margin-bottom: 0.8rem;
    }

    .slider-counter--dots {
      margin-top: 2rem;
    }

    .testimonial-slider__arrow {
      width: 2.2rem;
      height: 2.2rem;
      border-width: 1px;
    }

    .testimonial-slider__arrow--prev {
      left: -0.5rem;
    }

    .testimonial-slider__arrow--next {
      right: -0.5rem;
    }

    .testimonial-slider__arrow svg {
      width: 1rem;
      height: 1rem;
    }
  }

  @media screen and (max-width: 480px) {
    .testimonial-slider-section {
      padding: 3rem 0.5rem;
    }

    .testimonial-slider__slide {
      flex: 0 0 90%;
      max-width: 90%;
    }

    .testimonial-slider__title {
      font-size: 1.6rem;
      margin-bottom: 2.5rem;
    }

    .testimonial-card {
      padding: 1.8rem;
    }

    .testimonial-card__text {
      font-size: 0.9rem;
    }

    .testimonial-slider__arrow {
      display: none;
    }
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const slider = document.querySelector('#Slider-{{ section.id }}');
  const prevButton = document.querySelector('.testimonial-slider__arrow--prev');
  const nextButton = document.querySelector('.testimonial-slider__arrow--next');
  const dots = document.querySelectorAll('.slider-counter__link--dots');

  if (!slider || !prevButton || !nextButton) return;

  let currentIndex = 0;
  const slides = slider.querySelectorAll('.testimonial-slider__slide');
  const totalSlides = slides.length;
  const slidesToShow = window.innerWidth <= 749 ? 1 : (window.innerWidth <= 989 ? 2 : 3);
  const maxIndex = Math.max(0, totalSlides - slidesToShow);

  function updateSlider() {
    const slideWidth = slides[0].offsetWidth;
    const gap = parseInt(getComputedStyle(slider).gap) || 0;
    const translateX = currentIndex * (slideWidth + gap);
    slider.style.transform = `translateX(-${translateX}px)`;

    // Update dots
    dots.forEach((dot, index) => {
      dot.classList.toggle('is-active', index === currentIndex);
    });

    // Update arrow states
    prevButton.style.opacity = currentIndex === 0 ? '0.5' : '1';
    nextButton.style.opacity = currentIndex >= maxIndex ? '0.5' : '1';
  }

  prevButton.addEventListener('click', function() {
    if (currentIndex > 0) {
      currentIndex--;
      updateSlider();
    }
  });

  nextButton.addEventListener('click', function() {
    if (currentIndex < maxIndex) {
      currentIndex++;
      updateSlider();
    }
  });

  // Dot navigation
  dots.forEach((dot, index) => {
    dot.addEventListener('click', function() {
      currentIndex = Math.min(index, maxIndex);
      updateSlider();
    });
  });

  // Initialize
  updateSlider();

  // Handle resize
  window.addEventListener('resize', function() {
    const newSlidesToShow = window.innerWidth <= 749 ? 1 : (window.innerWidth <= 989 ? 2 : 3);
    const newMaxIndex = Math.max(0, totalSlides - newSlidesToShow);
    currentIndex = Math.min(currentIndex, newMaxIndex);
    updateSlider();
  });
});
</script>

{% schema %}
{
  "name": "Custom Testimonials",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "default": "Hear from Our Customers",
      "label": "Title"
    }
  ],
  "blocks": [
    {
      "type": "testimonial",
      "name": "Testimonial",
      "limit": 9,
      "settings": [
        {
          "type": "range",
          "id": "rating",
          "min": 1,
          "max": 5,
          "step": 1,
          "default": 5,
          "label": "Star rating"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>The variety of flavors they offer is incredible, and each one is more delicious than the last.</p>",
          "label": "Testimonial text"
        },
        {
          "type": "text",
          "id": "author",
          "default": "Anna Lou",
          "label": "Author name"
        },
        {
          "type": "text",
          "id": "product",
          "label": "Product purchased (optional)"
        },
        {
          "type": "checkbox",
          "id": "show_verified",
          "default": true,
          "label": "Show 'Verified Buyer' badge"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Custom Testimonials",
      "blocks": [
        {
          "type": "testimonial"
        },
        {
          "type": "testimonial"
        },
        {
          "type": "testimonial"
        }
      ]
    }
  ]
}
{% endschema %}
